#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include <Arduino.h>
#include "animations/Breather.h"
#include "audio/AudioManager.h"
#include "ui/Buttons.h"
#include "core/DeviceState.h"
#include "utils/sleeper.h"

// Global SD mutex — shared by all tasks accessing SD.
SemaphoreHandle_t sdMutex = nullptr;

// Use volatile because it's shared across cores
volatile DeviceState currentState = INITIALIZING;

TaskHandle_t audioTaskHandle;

// Forward declarations
void onButtonPress(button_id_t button);
void onLongPress(button_id_t button);
void onConnectionChanged(bool connected, const char* device_name);
void onPlaybackChanged(AudioState state, const char* filename);

void renderInitializing();
void renderSearchingTWS();
void renderConnecting();
void renderConnectedState();
void renderBrowsingFiles();
void renderPlayingState();
void renderPausedState();
void renderStoppedState();

// AudioManager FreeRTOS Task for Core 0
void audioTask(void *pvParameters) {
  Serial.println("[AUDIO_TASK] Starting audio task...");

  if (audioManager.init(5, "ESPOD")) {
    Serial.println("AudioManager initialized successfully");
    audioManager.setConnectionCallback(onConnectionChanged);
    audioManager.setPlaybackCallback(onPlaybackChanged);
    currentState = SEARCHING_TWS;
  } else {
    Serial.println("Failed to initialize AudioManager");
    currentState = OFF;
  }

  Serial.println("[AUDIO_TASK] Entering main loop...");
  unsigned long lastHeartbeat = 0;

  while (true) {
    audioManager.loop();

    // Heartbeat every 5 seconds to show the task is alive
    if (millis() - lastHeartbeat > 5000) {
      Serial.println("[AUDIO_TASK] Heartbeat - task is running");
      lastHeartbeat = millis();
    }

    vTaskDelay(10 / portTICK_PERIOD_MS); // Small delay to yield Core 0
  }
}

void setup() {
  Serial.begin(115200);
  Serial.println("\n\n=== ESPOD Bluetooth A2DP MP3 Player ===\n");
  delay(1000); // Give Serial time
  // Create SD mutex
  sdMutex = xSemaphoreCreateMutex();
  if (sdMutex == nullptr) {
    Serial.println("[ERROR] Failed to create SD mutex!");
    while (true); // Fatal error.
  }

  modifyJoystickForNormalUse();
  initBreather();
  initButtons(onButtonPress, onLongPress);

  // Start the audio task on Core 0
  xTaskCreatePinnedToCore(
    audioTask,           // Function to run
    "AudioTask",         // Name
    8192,                // Stack size
    NULL,                // Parameter
    1,                   // Priority
    &audioTaskHandle,    // Task handle
    0                    // Core 0
  );
}

void loop() {
  switch (currentState) {
    case OFF:
      sleepWell();
      break;

    case INITIALIZING:
      renderInitializing();
      break;

    case SEARCHING_TWS:
      renderSearchingTWS();
      break;

    case CONNECTING:
      renderConnecting();
      break;

    case CONNECTED:
      renderConnectedState();
      break;

    case BROWSING_FILES:
      renderBrowsingFiles();
      break;

    case PLAYING:
      renderPlayingState();
      break;

    case PAUSED:
      renderPausedState();
      break;

    case STOPPED:
      renderStoppedState();
      break;

    default:
      Serial.print("Unknown state: ");
      Serial.println(currentState);
      currentState = SEARCHING_TWS;
  }

  delay(100); // Keep loop responsive
}

// --- Renders ---
void renderInitializing() {
  Serial.println("Initializing audio system...");
  delay(1000);
}

void renderSearchingTWS() {
  Serial.println("Searching for TWS devices...");
  audioManager.startDiscovery();
  delay(2000);
}

void renderConnecting() {
  Serial.println("Connecting to device...");
  delay(1000);
}

void renderConnectedState() {
  Serial.println("Connected to: " + audioManager.getConnectedDeviceName());
  Serial.printf("Files available: %d\n", audioManager.getFileCount());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  } else if (audioManager.getFileCount() > 0) {
    currentState = BROWSING_FILES;
  }

  delay(2000);
}

void renderBrowsingFiles() {
  if (audioManager.isBusy()) {
    Serial.printf("Browsing files (%d/%d): %s [BUSY: %d]\n",
                  audioManager.getCurrentFileIndex() + 1,
                  audioManager.getFileCount(),
                  audioManager.getCurrentFile().c_str(),
                  audioManager.getBusyOperation());
  } else {
    Serial.printf("Browsing files (%d/%d): %s\n",
                  audioManager.getCurrentFileIndex() + 1,
                  audioManager.getFileCount(),
                  audioManager.getCurrentFile().c_str());
  }
  delay(1000);
}

void renderPlayingState() {
  String currentFile = audioManager.getCurrentFile();
  int currentPosition = audioManager.getCurrentPosition(); // Now using AudioTimer for accurate timing
  int totalDuration = audioManager.getTotalDuration();     // Duration from MP3 metadata

  // Format time as MM:SS
  int currentMin = currentPosition / 60;
  int currentSec = currentPosition % 60;
  int totalMin = totalDuration / 60;
  int totalSec = totalDuration % 60;

  if (audioManager.isBusy()) {
    Serial.printf("Playing: %s [%02d:%02d/%02d:%02d] [BUSY: %d]\n",
                  currentFile.c_str(),
                  currentMin, currentSec,
                  totalMin, totalSec,
                  audioManager.getBusyOperation());
  } else {
    Serial.printf("Playing: %s [%02d:%02d/%02d:%02d]\n",
                  currentFile.c_str(),
                  currentMin, currentSec,
                  totalMin, totalSec);
  }

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

void renderPausedState() {
  Serial.printf("Paused: %s\n", audioManager.getCurrentFile().c_str());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

void renderStoppedState() {
  Serial.printf("Stopped: %s\n", audioManager.getCurrentFile().c_str());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// --- Button Handlers ---
void onButtonPress(button_id_t button) {
  Serial.print("Button Pressed: ");
  Serial.println(button);

  switch (currentState) {
    case OFF:
      break;

    case INITIALIZING:
      break;

    case SEARCHING_TWS:
      if (button == BTN_CENTER) {
        audioManager.connectToDevice("TWS");
        currentState = CONNECTING;
      }
      break;

    case CONNECTING:
      break;

    case CONNECTED:
      if (button == BTN_CENTER) {
        currentState = BROWSING_FILES;
      }
      break;

    case BROWSING_FILES:
      if (button == BTN_UP) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_DOWN) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPlay();
        currentState = PLAYING;
      }
      break;

    case PLAYING:
      if (button == BTN_UP) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_RIGHT) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPause();
        currentState = PAUSED;
      }
      break;

    case PAUSED:
    case STOPPED:
      if (button == BTN_UP) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_RIGHT) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPlay();
        currentState = PLAYING;
      }
      break;

    default:
      Serial.println("Unknown state for button press");
  }
}

void onLongPress(button_id_t button) {
  Serial.printf("Long press detected on button: %d\n", button);

  if (button == BTN_RST) {
    if (currentState == OFF) {
      Serial.println("Waking up device...");
      currentState = SEARCHING_TWS;
    } else {
      Serial.println("Turning off device...");
      if (audioManager.isConnected()) {
        audioManager.disconnect();
      }
      currentState = OFF;
    }
  }
}

// --- Audio Manager callbacks ---
void onConnectionChanged(bool connected, const char* device_name) {
  if (connected) {
    Serial.printf("Connected to: %s\n", device_name);
    currentState = CONNECTED;
  } else {
    Serial.println("Disconnected from device");
    currentState = SEARCHING_TWS;
  }
}

void onPlaybackChanged(AudioState state, const char* filename) {
  Serial.printf("Playback state changed: %d, file: %s\n", state, filename);

  switch (state) {
    case AUDIO_PLAYING:
      currentState = PLAYING;
      break;
    case AUDIO_PAUSED:
      currentState = PAUSED;
      break;
    case AUDIO_STOPPED:
      currentState = STOPPED;
      break;
    case AUDIO_CONNECTING:
      currentState = CONNECTING;
      break;
    case AUDIO_DISCONNECTED:
      currentState = SEARCHING_TWS;
      break;
  }
}
