#pragma once

#include <Arduino.h>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "AudioTools.h"
#include "AudioTools/Disk/AudioSourceSDFAT.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"
#include "AudioTools/AudioCodecs/HeaderParserMP3.h"
#include "AudioTools/AudioLibs/A2DPStream.h"
#include "AudioTools/CoreAudio/AudioTimer.h"

// External declaration of the global SD mutex
extern SemaphoreHandle_t sdMutex;

// Audio playback states
enum AudioState
{
  AUDIO_STOPPED,
  AUDIO_PLAYING,
  AUDIO_PAUSED,
  AUDIO_CONNECTING,
  AUDIO_DISCONNECTED
};

// Busy operation types
enum BusyOperation
{
  BUSY_NONE,
  BUSY_NEXT_SONG,
  BUSY_PREV_SONG,
  BUSY_PLAY,
  BUSY_PAUSE,
  BUSY_STOP,
  BUSY_CALCULATE_DURATION
};

class MyAudioSource : public audio_tools::AudioSourceSDFAT
{
public:
  using AudioSourceSDFAT::AudioSourceSDFAT; // inherit constructor

  uint32_t fileSize()
  {
    return file.size();
  }

  // Safe version that doesn't use mutex (for when mutex is already held)
  int getMP3DurationSafe() {
    Serial.println("[DURATION_SAFE] Calculating duration without mutex...");

    // Use the known path of the currently open file.
    const char* path = toStr();  // Safe: gives the file path.
    if (path == nullptr || strlen(path) == 0) {
      Serial.println("[DURATION_SAFE] Invalid file path!");
      return 180;
    }
    Serial.printf("[DURATION_SAFE] File path: %s\n", path);

    // For now, return default duration to avoid file I/O issues
    // In a real implementation, you would parse the MP3 file here
    Serial.println("[DURATION_SAFE] Returning default duration");
    return 180;
  }

  int getMP3Duration() {
    Serial.println("[DURATION] Attempting to take SD mutex...");
    if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
      Serial.println("[DURATION] Failed to take SD mutex - timeout!");
      return 180; // Return default duration instead of 0
    }

    Serial.println("[DURATION] SD mutex acquired, calculating duration...");

    // Use the known path of the currently open file.
    const char* path = toStr();  // Safe: gives the file path.
    if (path == nullptr || strlen(path) == 0) {
      Serial.println("[DURATION] Invalid file path!");
      xSemaphoreGive(sdMutex);
      return 180;
    }
    Serial.printf("[DURATION] File path: %s\n", path);

    AudioFile tempFile;
    if (!tempFile.open(path, O_RDONLY)) {
      Serial.printf("[DURATION] Failed to open file for duration: %s\n", path);
      xSemaphoreGive(sdMutex);
      return 180;
    }
    Serial.println("[DURATION] File opened successfully for duration!");

    uint8_t tmp[1024];
    Serial.println("[DURATION] About to read bytes from file...");
    int len = tempFile.readBytes((char*)tmp, 1024);
    Serial.printf("[DURATION] Read %d bytes from file\n", len);

    if (len <= 0) {
      Serial.println("[DURATION] Failed to read any bytes from file");
      tempFile.close();
      xSemaphoreGive(sdMutex);
      return 180;
    }

    Serial.println("[DURATION] Creating MP3 parser...");
    HeaderParserMP3 parser;
    Serial.println("[DURATION] Validating MP3 header...");

    if (!parser.isValid(tmp, len)) {
      Serial.printf("[DURATION] Not a valid MP3: %s\n", path);
      tempFile.close();
      xSemaphoreGive(sdMutex);
      return 180;
    }
    Serial.println("[DURATION] File is a valid MP3!");

    Serial.println("[DURATION] Getting file size...");
    size_t fileSize = tempFile.size();
    Serial.printf("[DURATION] File size: %zu bytes\n", fileSize);

    Serial.println("[DURATION] Calculating playing time...");
    int duration = parser.getPlayingTime(fileSize);
    Serial.printf("[DURATION] Calculated duration: %d seconds\n", duration);

    tempFile.close();
    Serial.println("[DURATION] File closed, releasing SD mutex...");

    // ======== UNLOCK SD =========
    xSemaphoreGive(sdMutex);
    Serial.println("[DURATION] SD mutex released");

    Serial.printf("[DURATION] Final duration: %d seconds\n", duration);
    return duration > 0 ? duration : 180; // Return calculated duration or default
  }

};

// Audio manager class for handling SD card MP3 playback and Bluetooth A2DP streaming
class AudioManager
{
public:
  AudioManager();
  ~AudioManager();

  // Initialize audio system
  bool init(int sd_cs_pin = 5, const char *device_name = "ESPOD");

  // Bluetooth device discovery and connection
  bool startDiscovery();
  bool connectToDevice(const char *device_name);
  bool isConnected();
  String getConnectedDeviceName();
  bool disconnect();

  // Audio playback controls
  bool play();
  bool pause();
  bool stop();
  bool nextTrack();
  bool previousTrack();
  bool setVolume(int volume); // 0-100
  int getVolume();

  // File management
  bool setCurrentFile(const char *filename);
  String getCurrentFile();
  bool hasNextFile();
  bool hasPreviousFile();
  int getFileCount();
  int getCurrentFileIndex();

  // State management
  AudioState getState();
  bool isPlaying();
  bool isPaused();
  bool isBusy();
  BusyOperation getBusyOperation();

  // Non-blocking operations (set flags for processing in loop)
  bool requestNextTrack();
  bool requestPreviousTrack();
  bool requestPlay();
  bool requestPause();
  bool requestStop();

  // Main loop function - must be called regularly
  void loop();

  // Callbacks for state changes
  void setConnectionCallback(void (*callback)(bool connected, const char *device_name));
  void setPlaybackCallback(void (*callback)(AudioState state, const char *filename));

  int getCurrentPosition(); // Get current playback position in seconds
  int getTotalDuration();   // Get total duration of current file in seconds

private:
  // Audio components
  MyAudioSource *source;
  MP3DecoderHelix *decoder;
  BufferRTOS<uint8_t> *buffer;
  QueueStream<uint8_t> *out;
  AudioPlayer *player;
  BluetoothA2DPSource *a2dp_source;

  // State variables
  AudioState current_state;
  String connected_device_name;
  String current_file;
  int current_volume;
  int sd_cs_pin;

  // Busy state management
  volatile bool busy;
  volatile BusyOperation busy_for;

  // File management
  int file_count;
  int current_file_index;

  // Playback timing
  unsigned long playback_start_time;
  int current_position; // Current position in seconds
  int total_duration;   // Total duration in seconds

  // AudioTimer for accurate timing
  audio_tools::TimerAlarmRepeating position_timer;
  volatile bool timer_active;

  // Callbacks
  void (*connection_callback)(bool connected, const char *device_name);
  void (*playback_callback)(AudioState state, const char *filename);

  // Internal methods
  void updateFileList();
  bool initializeAudioComponents();
  void cleanupAudioComponents();
  bool calculateMP3Duration(const char *filename);
  int calculateMP3DurationInternal(const char *filename); // Internal version without mutex
  static int32_t getData(uint8_t *data, int32_t bytes);
  static bool deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi);
  static void connectionStateChanged(esp_a2d_connection_state_t state, void *ptr);

  // Busy operation processing (called from loop)
  void processBusyOperations();
  void executeNextTrack();
  void executePreviousTrack();
  void executePlay();
  void executePause();
  void executeStop();

  // Timer callback for position tracking
  static void positionTimerCallback(void *obj);
  void startPositionTimer();
  void stopPositionTimer();

  // Static instance for callbacks
  static AudioManager *instance;
};

// Global audio manager instance
extern AudioManager audioManager;
