#pragma once

#include <Arduino.h>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "AudioTools.h"
#include "AudioTools/Disk/AudioSourceSDFAT.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"
#include "AudioTools/AudioCodecs/HeaderParserMP3.h"
#include "AudioTools/AudioLibs/A2DPStream.h"
#include "AudioTools/CoreAudio/AudioTimer.h"

// External declaration of the global SD mutex
extern SemaphoreHandle_t sdMutex;

// Audio playback states
enum AudioState
{
  AUDIO_STOPPED,
  AUDIO_PLAYING,
  AUDIO_PAUSED,
  AUDIO_CONNECTING,
  AUDIO_DISCONNECTED
};

class MyAudioSource : public audio_tools::AudioSourceSDFAT
{
public:
  using AudioSourceSDFAT::AudioSourceSDFAT; // inherit constructor

  uint32_t fileSize()
  {
    return file.size();
  }

  int getMP3Duration() {
    if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(2000)) != pdTRUE) {
      Serial.println("[DURATION] Failed to take SD mutex!");
      return 0;
    }

    // Debug print
    Serial.println("[DURATION] Calculating duration...");
    // Use the known path of the currently open file.
    const char* path = toStr();  // Safe: gives the file path.
    if (path == nullptr || strlen(path) == 0) {
      Serial.println("[DURATION] Invalid file path!");
      xSemaphoreGive(sdMutex);
      return 0;
    }
    Serial.printf("[DURATION] File path: %s\n", path);

    AudioFile tempFile;
    if (!tempFile.open(path, O_RDONLY)) {
      Serial.printf("[DURATION] Failed to open file for duration: %s\n", path);
      xSemaphoreGive(sdMutex);
      return 0;
    }
    Serial.println("[DURATION] File opened successfully for duration!");

    uint8_t tmp[1024];
    int len = tempFile.readBytes((char*)tmp, 1024);

    HeaderParserMP3 parser;
    if (!parser.isValid(tmp, len)) {
      Serial.printf("[DURATION] Not a valid MP3: %s\n", path);
      tempFile.close();
      xSemaphoreGive(sdMutex);
      return 0;
    }
    Serial.println("[DURATION] File is a valid MP3!");

    size_t fileSize = tempFile.size();
    int duration = parser.getPlayingTime(fileSize);

    tempFile.close();
    // ======== UNLOCK SD =========
    xSemaphoreGive(sdMutex);

    Serial.printf("[DURATION] Duration calculated: %d seconds\n", duration);
    return duration;
  }

};

// Audio manager class for handling SD card MP3 playback and Bluetooth A2DP streaming
class AudioManager
{
public:
  AudioManager();
  ~AudioManager();

  // Initialize audio system
  bool init(int sd_cs_pin = 5, const char *device_name = "ESPOD");

  // Bluetooth device discovery and connection
  bool startDiscovery();
  bool connectToDevice(const char *device_name);
  bool isConnected();
  String getConnectedDeviceName();
  bool disconnect();

  // Audio playback controls
  bool play();
  bool pause();
  bool stop();
  bool nextTrack();
  bool previousTrack();
  bool setVolume(int volume); // 0-100
  int getVolume();

  // File management
  bool setCurrentFile(const char *filename);
  String getCurrentFile();
  bool hasNextFile();
  bool hasPreviousFile();
  int getFileCount();
  int getCurrentFileIndex();

  // State management
  AudioState getState();
  bool isPlaying();
  bool isPaused();

  // Main loop function - must be called regularly
  void loop();

  // Callbacks for state changes
  void setConnectionCallback(void (*callback)(bool connected, const char *device_name));
  void setPlaybackCallback(void (*callback)(AudioState state, const char *filename));

  int getCurrentPosition(); // Get current playback position in seconds
  int getTotalDuration();   // Get total duration of current file in seconds

private:
  // Audio components
  MyAudioSource *source;
  MP3DecoderHelix *decoder;
  BufferRTOS<uint8_t> *buffer;
  QueueStream<uint8_t> *out;
  AudioPlayer *player;
  BluetoothA2DPSource *a2dp_source;

  // State variables
  AudioState current_state;
  String connected_device_name;
  String current_file;
  int current_volume;
  int sd_cs_pin;

  // File management
  int file_count;
  int current_file_index;

  // Playback timing
  unsigned long playback_start_time;
  int current_position; // Current position in seconds
  int total_duration;   // Total duration in seconds

  // AudioTimer for accurate timing
  audio_tools::TimerAlarmRepeating position_timer;
  volatile bool timer_active;

  // Callbacks
  void (*connection_callback)(bool connected, const char *device_name);
  void (*playback_callback)(AudioState state, const char *filename);

  // Internal methods
  void updateFileList();
  bool initializeAudioComponents();
  void cleanupAudioComponents();
  bool calculateMP3Duration(const char *filename);
  static int32_t getData(uint8_t *data, int32_t bytes);
  static bool deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi);
  static void connectionStateChanged(esp_a2d_connection_state_t state, void *ptr);

  // Timer callback for position tracking
  static void positionTimerCallback(void *obj);
  void startPositionTimer();
  void stopPositionTimer();

  // Static instance for callbacks
  static AudioManager *instance;
};

// Global audio manager instance
extern AudioManager audioManager;
