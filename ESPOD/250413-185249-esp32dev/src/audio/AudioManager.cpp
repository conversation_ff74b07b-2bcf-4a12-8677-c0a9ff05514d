#include "AudioManager.h"

// Static instance for callbacks
AudioManager* AudioManager::instance = nullptr;

// Global audio manager instance
AudioManager audioManager;

// callback for meta data
void printMetaData(MetaDataType type, const char* str, int len){
  Serial.print("==> ");
  Serial.print(toStr(type));
  Serial.print(": ");
  Serial.println(str);
}

AudioManager::AudioManager() {
    source = nullptr;
    decoder = nullptr;
    buffer = nullptr;
    out = nullptr;
    player = nullptr;
    a2dp_source = nullptr;

    current_state = AUDIO_DISCONNECTED;
    connected_device_name = "";
    current_file = "";
    current_volume = 80;
    sd_cs_pin = 5;
    file_count = 0;
    current_file_index = 0;

    connection_callback = nullptr;
    playback_callback = nullptr;

    playback_start_time = 0;
    current_position = 0;
    total_duration = 0;
    timer_active = false;

    instance = this;
}

AudioManager::~AudioManager() {
    stopPositionTimer();
    cleanupAudioComponents();
}

bool AudioManager::init(int sd_cs_pin, const char* device_name) {
    Serial.println("Initializing AudioManager...");
    
    this->sd_cs_pin = sd_cs_pin;
    
    // Initialize AudioTools logger
    AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Error);
    
    // Initialize audio components
    if (!initializeAudioComponents()) {
        Serial.println("Failed to initialize audio components");
        return false;
    }
    
    // Initialize A2DP source
    a2dp_source = new BluetoothA2DPSource();
    if (!a2dp_source) {
        Serial.println("Failed to create A2DP source");
        return false;
    }
    
    // Set up A2DP callbacks
    a2dp_source->set_ssid_callback(deviceScanCallback);
    a2dp_source->set_auto_reconnect(false);
    a2dp_source->set_on_connection_state_changed(connectionStateChanged);
    a2dp_source->set_data_callback(getData);
    a2dp_source->set_volume(current_volume);
    
    // Start A2DP with device name
    a2dp_source->start(device_name);
    
    current_state = AUDIO_DISCONNECTED;
    
    Serial.println("AudioManager initialized successfully");
    return true;
}

bool AudioManager::initializeAudioComponents() {
    const int buffer_size = 2 * 1024;
    const char *startFilePath = "/";
    const char *ext = "mp3";
    
    try {
        // Create audio source for SD card
        source = new MyAudioSource(startFilePath, ext, sd_cs_pin);
        // source->setMetadataCallback(printMetaData);
        if (!source) {
            Serial.println("Failed to create audio source");
            return false;
        }
        
        // Create MP3 decoder
        decoder = new MP3DecoderHelix();
        if (!decoder) {
            Serial.println("Failed to create MP3 decoder");
            return false;
        }
        
        // Create buffer
        buffer = new BufferRTOS<uint8_t>(0);
        if (!buffer) {
            Serial.println("Failed to create buffer");
            return false;
        }
        buffer->resize(buffer_size);
        
        // Create queue stream
        out = new QueueStream<uint8_t>(*buffer);
        if (!out) {
            Serial.println("Failed to create queue stream");
            return false;
        }
        out->begin(95); // Start when 95% full
        
        // Create audio player
        player = new AudioPlayer(*source, *out, *decoder);
        if (!player) {
            Serial.println("Failed to create audio player");
            return false;
        }
        player->setDelayIfOutputFull(0);
        player->setVolume(1);
        player->begin();
        
        // Update file list
        updateFileList();
        
        return true;
    } catch (...) {
        Serial.println("Exception during audio component initialization");
        cleanupAudioComponents();
        return false;
    }
}

void AudioManager::cleanupAudioComponents() {
    if (player) {
        delete player;
        player = nullptr;
    }
    if (out) {
        delete out;
        out = nullptr;
    }
    if (buffer) {
        delete buffer;
        buffer = nullptr;
    }
    if (decoder) {
        delete decoder;
        decoder = nullptr;
    }
    if (source) {
        delete source;
        source = nullptr;
    }
    if (a2dp_source) {
        delete a2dp_source;
        a2dp_source = nullptr;
    }
}

bool AudioManager::startDiscovery() {
    if (!a2dp_source) {
        Serial.println("A2DP source not initialized");
        return false;
    }
    
    Serial.println("Starting Bluetooth device discovery...");
    // The AudioTools A2DP will automatically scan for devices
    return true;
}

bool AudioManager::connectToDevice(const char* device_name) {
    if (!a2dp_source) {
        Serial.println("A2DP source not initialized");
        return false;
    }
    
    Serial.printf("Attempting to connect to device: %s\n", device_name);
    current_state = AUDIO_CONNECTING;
    
    // The connection will be handled by the A2DP source automatically
    // when a matching device is found via the scan callback
    return true;
}

bool AudioManager::isConnected() {
    return a2dp_source && a2dp_source->is_connected();
}

String AudioManager::getConnectedDeviceName() {
    return connected_device_name;
}

bool AudioManager::disconnect() {
    if (!a2dp_source) {
        return false;
    }
    
    a2dp_source->disconnect();
    current_state = AUDIO_DISCONNECTED;
    connected_device_name = "";
    
    if (connection_callback) {
        connection_callback(false, "");
    }
    
    return true;
}

bool AudioManager::play() {
    if (!isConnected() || !player) {
        Serial.println("Cannot play: not connected or player not initialized");
        return false;
    }
    
    if (current_file.isEmpty() && file_count > 0) {
        // Take SD mutex for file access
        if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
            // Set first file if none selected
            setCurrentFile(source->toStr());
            xSemaphoreGive(sdMutex);
        } else {
            Serial.println("[PLAY] Failed to take SD mutex for file selection!");
            return false;
        }
    }

    // Always update current file to match what the source is actually playing
    if (source && file_count > 0) {
        // Take SD mutex for file access
        if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
            current_file = source->toStr();
            xSemaphoreGive(sdMutex);
        } else {
            Serial.println("[PLAY] Failed to take SD mutex for file update!");
            return false;
        }
    }

    if (current_file.isEmpty()) {
        Serial.println("No file to play");
        return false;
    }
    
    if (current_state != AUDIO_PLAYING) {
        playback_start_time = millis();
        current_position = 0;

        // For MP3 files, try to get duration from file metadata
        // This is a simplified approach - in a real implementation,
        // you would parse the MP3 header or use ID3 tags
        if (source && !current_file.isEmpty()) {
            // Use default duration to avoid mutex issues during playback
            total_duration = 180;
            Serial.printf("[PLAY] Using default duration: %d seconds\n", total_duration);

            // TODO: Calculate duration in a separate task or during file loading
            // to avoid mutex conflicts during playback
        }

        // Start accurate position timer
        startPositionTimer();
    }
    
    current_state = AUDIO_PLAYING;
    
    if (playback_callback) {
        playback_callback(current_state, current_file.c_str());
    }
    
    Serial.printf("Playing: %s\n", current_file.c_str());
    return true;
}

bool AudioManager::pause() {
    if (current_state != AUDIO_PLAYING) {
        return false;
    }

    // Stop position timer and save current position
    stopPositionTimer();

    current_state = AUDIO_PAUSED;

    if (playback_callback) {
        playback_callback(current_state, current_file.c_str());
    }

    Serial.println("Audio paused");
    return true;
}

bool AudioManager::stop() {
    if (current_state == AUDIO_STOPPED) {
        return true;
    }

    // Stop position timer and reset position
    stopPositionTimer();
    current_position = 0;

    current_state = AUDIO_STOPPED;

    if (playback_callback) {
        playback_callback(current_state, current_file.c_str());
    }

    Serial.println("Audio stopped");
    return true;
}

bool AudioManager::nextTrack() {
    if (!player || file_count <= 1) {
        Serial.println("Next track: No more files available");
        return false;
    }

    // Take SD mutex for track navigation
    if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        Serial.println("[NEXT] Failed to take SD mutex!");
        return false;
    }

    // Move to next file using the player
    player->next();
    current_file_index = (current_file_index + 1) % file_count;

    // Update current file name from source
    if (source) {
        current_file = source->toStr();
    }

    // Release SD mutex
    xSemaphoreGive(sdMutex);

    Serial.printf("Next track: %s (%d/%d)\n", current_file.c_str(), current_file_index + 1, file_count);

    // If currently playing, restart timing with new file
    if (current_state == AUDIO_PLAYING) {
        stopPositionTimer();
        playback_start_time = millis();
        current_position = 0;
        // Use default duration to avoid mutex conflicts during playback
        total_duration = 180;
        startPositionTimer();
    }

    return true;
}

bool AudioManager::previousTrack() {
    if (!player || file_count <= 1) {
        Serial.println("Previous track: No more files available");
        return false;
    }

    // Take SD mutex for track navigation
    if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        Serial.println("[PREV] Failed to take SD mutex!");
        return false;
    }

    // Move to previous file using the player
    player->previous();
    current_file_index = (current_file_index - 1 + file_count) % file_count;

    // Update current file name from source
    if (source) {
        current_file = source->toStr();
    }

    // Release SD mutex
    xSemaphoreGive(sdMutex);

    Serial.printf("Previous track: %s (%d/%d)\n", current_file.c_str(), current_file_index + 1, file_count);

    // If currently playing, restart timing with new file
    if (current_state == AUDIO_PLAYING) {
        stopPositionTimer();
        playback_start_time = millis();
        current_position = 0;
        // Use default duration to avoid mutex conflicts during playback
        total_duration = 180;
        startPositionTimer();
    }

    return true;
}

bool AudioManager::setVolume(int volume) {
    if (volume < 0 || volume > 100) {
        return false;
    }
    
    current_volume = volume;
    
    if (a2dp_source) {
        a2dp_source->set_volume(volume);
    }
    
    Serial.printf("Volume set to: %d\n", volume);
    return true;
}

int AudioManager::getVolume() {
    return current_volume;
}

bool AudioManager::setCurrentFile(const char* filename) {
    if (!filename) {
        return false;
    }
    
    current_file = filename;
    Serial.printf("Current file set to: %s\n", filename);
    
    // Calculate duration when file is set
    calculateMP3Duration(filename);
    
    return true;
}

String AudioManager::getCurrentFile() {
    return current_file;
}

bool AudioManager::hasNextFile() {
    return file_count > 1;
}

bool AudioManager::hasPreviousFile() {
    return file_count > 1;
}

int AudioManager::getFileCount() {
    return file_count;
}

int AudioManager::getCurrentFileIndex() {
    return current_file_index;
}

AudioState AudioManager::getState() {
    return current_state;
}

bool AudioManager::isPlaying() {
    return current_state == AUDIO_PLAYING;
}

bool AudioManager::isPaused() {
    return current_state == AUDIO_PAUSED;
}

void AudioManager::loop() {
    if (player && (current_state == AUDIO_PLAYING)) {
        // Take SD mutex with a short timeout for audio streaming
        if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(5)) == pdTRUE) {
            // Decode data to buffer
            player->copy();
            // Release SD mutex immediately after reading
            xSemaphoreGive(sdMutex);
        } else {
            // If we can't get the mutex quickly, skip this iteration
            // This prevents audio stuttering when other operations are using SD
            // Reduce frequency of this message to avoid spam
            static unsigned long lastMutexWarning = 0;
            if (millis() - lastMutexWarning > 1000) {
                Serial.println("[AUDIO] SD mutex busy, skipping audio read");
                lastMutexWarning = millis();
            }
        }
    }
}

void AudioManager::setConnectionCallback(void (*callback)(bool connected, const char* device_name)) {
    connection_callback = callback;
}

void AudioManager::setPlaybackCallback(void (*callback)(AudioState state, const char* filename)) {
    playback_callback = callback;
}

void AudioManager::updateFileList() {
    if (!source) {
        file_count = 0;
        return;
    }

    // Take SD mutex before accessing SD card
    if (xSemaphoreTake(sdMutex, pdMS_TO_TICKS(2000)) != pdTRUE) {
        Serial.println("[FILELIST] Failed to take SD mutex!");
        file_count = 0;
        return;
    }

    // Get the actual file count from the source
    file_count = source->size();
    current_file_index = 0;

    // Get the current filename from the source
    if (file_count > 0) {
        current_file = source->toStr();
        Serial.printf("Found %d MP3 files on SD card. Current file: %s\n", file_count, current_file.c_str());

        // Calculate duration for the first file while we have the mutex
        if (source && !current_file.isEmpty()) {
            Serial.println("[FILELIST] Calculating duration for first file...");
            // Use the internal getMP3Duration method that doesn't use mutex
            total_duration = calculateMP3DurationInternal(current_file.c_str());
            Serial.printf("[FILELIST] Duration for %s: %d seconds\n", current_file.c_str(), total_duration);
        }
    } else {
        current_file = "";
        Serial.println("No MP3 files found on SD card");
    }

    // Release SD mutex
    xSemaphoreGive(sdMutex);
}

// Static callback methods
int32_t AudioManager::getData(uint8_t *data, int32_t bytes) {
    if (!instance || !instance->buffer) {
        return 0;
    }
    
    size_t result_bytes = instance->buffer->readArray(data, bytes);
    return result_bytes;
}

bool AudioManager::deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi) {
    if (!instance) {
        return false;
    }
    
    Serial.printf("Found device: %s (RSSI: %d)\n", ssid, rssi);
    
    // For now, accept any device that looks like TWS earphones
    // You can customize this logic based on your specific device names
    if (strstr(ssid, "Airdopes") || strstr(ssid, "TWS") || strstr(ssid, "Earbuds") || 
        strstr(ssid, "LEXON") || strstr(ssid, "Buds")) {
        Serial.printf("Accepting device: %s\n", ssid);
        return true;
    }
    
    return false;
}

void AudioManager::connectionStateChanged(esp_a2d_connection_state_t state, void *ptr) {
    if (!instance) {
        return;
    }
    
    switch (state) {
        case ESP_A2D_CONNECTION_STATE_CONNECTED:
            instance->current_state = AUDIO_STOPPED;
            instance->connected_device_name = "Connected Device"; // A2DP doesn't provide device name directly
            Serial.println("Bluetooth A2DP connected");
            if (instance->connection_callback) {
                instance->connection_callback(true, instance->connected_device_name.c_str());
            }
            break;
            
        case ESP_A2D_CONNECTION_STATE_DISCONNECTED:
            instance->current_state = AUDIO_DISCONNECTED;
            instance->connected_device_name = "";
            Serial.println("Bluetooth A2DP disconnected");
            if (instance->connection_callback) {
                instance->connection_callback(false, "");
            }
            break;
            
        case ESP_A2D_CONNECTION_STATE_CONNECTING:
            instance->current_state = AUDIO_CONNECTING;
            Serial.println("Bluetooth A2DP connecting...");
            break;
            
        case ESP_A2D_CONNECTION_STATE_DISCONNECTING:
            Serial.println("Bluetooth A2DP disconnecting...");
            break;
    }
}

int AudioManager::getCurrentPosition() {
    // Return current position tracked by timer
    // Make sure we don't exceed total duration
    if (total_duration > 0 && current_position > total_duration) {
        current_position = total_duration;
    }

    return current_position;
}

int AudioManager::getTotalDuration() {
    return total_duration;
}

// Add this method to calculate MP3 duration
bool AudioManager::calculateMP3Duration(const char* filename) {
    // This is a simplified approach - in a real implementation,
    // you would use minimp3 to parse the file and calculate duration

    if (!source) {
        return false;
    }

    // For now, just set a default duration
    // In a real implementation, you would:
    // 1. Open the MP3 file
    // 2. Parse headers to get frame count, sample rate, etc.
    // 3. Calculate duration = (frame_count * samples_per_frame) / sample_rate

    total_duration = 180; // Default 3 minutes

    // Call this method when setting the current file
    return true;
}

// Internal method that calculates duration without taking mutex (assumes mutex is already held)
int AudioManager::calculateMP3DurationInternal(const char* filename) {
    if (!filename) {
        return 180; // Default duration
    }

    Serial.printf("[DURATION_INTERNAL] Calculating duration for: %s\n", filename);

    // For now, return a default duration
    // In a real implementation, you would parse the MP3 file here
    // This method assumes the caller already holds the SD mutex

    return 180; // Default 3 minutes
}

// Timer callback for accurate position tracking
void AudioManager::positionTimerCallback(void* obj) {
    AudioManager* manager = static_cast<AudioManager*>(obj);
    if (manager && manager->timer_active && manager->current_state == AUDIO_PLAYING) {
        manager->current_position++;
    }
}

void AudioManager::startPositionTimer() {
    if (timer_active) {
        stopPositionTimer();
    }

    // Set up timer to fire every 1000ms (1 second) for position tracking
    position_timer.setCallbackParameter(this);
    timer_active = position_timer.begin(positionTimerCallback, 1000, audio_tools::MS);

    if (timer_active) {
        Serial.println("Position timer started");
    } else {
        Serial.println("Failed to start position timer");
    }
}

void AudioManager::stopPositionTimer() {
    if (timer_active) {
        position_timer.end();
        timer_active = false;
        Serial.println("Position timer stopped");
    }
}
